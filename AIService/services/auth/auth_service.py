import httpx
from typing import Optional
from loguru import logger
from schemas.auth_schemas import AuthResponse, CurrentUser, AuthError
from config import settings


class AuthService:
    """Service for validating tokens against the external auth service"""
    
    def __init__(self, auth_url: str = "https://qa.kontratar.com:3008/api/auth/verify"):
        self.auth_url = auth_url
        self.timeout = 10.0  # 10 second timeout
    
    async def verify_token(self, token: str) -> Optional[CurrentUser]:
        """
        Verify a bearer token against the external auth service
        
        Args:
            token: The bearer token to verify (without 'Bearer ' prefix)
            
        Returns:
            CurrentUser if token is valid, None if invalid
            
        Raises:
            Exception: If auth service is unreachable or returns unexpected response
        """
        try:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient(timeout=self.timeout, verify=False) as client:
                logger.debug(f"Verifying token with auth service: {self.auth_url}")
                
                response = await client.get(self.auth_url, headers=headers)
                
                if response.status_code == 200:
                    auth_data = response.json()
                    logger.debug(f"Auth service response: {auth_data}")
                    
                    # Parse the auth response
                    auth_response = AuthResponse(**auth_data)
                    
                    # Create and return current user
                    current_user = CurrentUser.from_auth_response(auth_response)
                    logger.info(f"Token verified successfully for user: {current_user.email}")
                    
                    return current_user
                    
                elif response.status_code == 401:
                    logger.warning("Token verification failed: Invalid or expired token")
                    return None
                    
                else:
                    logger.error(f"Auth service returned unexpected status: {response.status_code}")
                    logger.error(f"Response body: {response.text}")
                    raise Exception(f"Auth service error: {response.status_code}")
                    
        except httpx.TimeoutException:
            logger.error("Auth service request timed out")
            raise Exception("Authentication service timeout")
            
        except httpx.RequestError as e:
            logger.error(f"Auth service request failed: {e}")
            raise Exception("Authentication service unavailable")
            
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            raise


# Global auth service instance
auth_service = AuthService()
