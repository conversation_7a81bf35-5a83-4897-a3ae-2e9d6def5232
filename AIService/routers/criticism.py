from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from database import get_customer_db
from controllers.customer.proposal_criticism_controller import ProposalCriticismController
from services.queue_service.proposal_criticism_queue_service import ProposalCriticismQueueService
from dependencies.auth import get_current_user
from schemas.auth_schemas import CurrentUser
from loguru import logger

router = APIRouter(prefix="/criticism", tags=["criticism"])

class CriticismQueueCreate(BaseModel):
    opportunity_id: str = Field(..., description="Opportunity identifier")
    tenant_id: str = Field(..., description="Tenant identifier")
    client_short_name: str = Field(..., description="Client short name")
    priority: int = Field(default=1, description="Analysis priority (1=high, 2=medium, 3=low)")
    analysis_type: str = Field(default="full", description="Type of analysis (full, structure, content, formatting, technical.)")
    submitted_by: Optional[str] = Field(default=None, description="User who submitted the analysis request")

class CriticismQueueStatusUpdate(BaseModel):
    status: str = Field(..., description="New status (NEW, PROCESSING, COMPLETED, FAILED)")
    analysis_results: Optional[Dict[str, Any]] = Field(default=None, description="Analysis results if completed")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")

class CriticismResultsResponse(BaseModel):
    success: bool
    results: List[Dict[str, Any]]
    statistics: Optional[Dict[str, Any]] = None


@router.get("/queue/new")
async def get_new_criticism_queue_items(
    limit: int = 10,
    db: AsyncSession = Depends(get_customer_db)
):
    """Get new criticism queue items waiting for analysis"""
    try:
        items = await ProposalCriticismQueueService.get_new_items(db, limit)
        return {
            "items": [
                {
                    "id": item.id,
                    "opportunity_id": item.opportunity_id,
                    "tenant_id": item.tenant_id,
                    "client_short_name": item.client_short_name,
                    "status": item.status,
                    "priority": item.priority,
                    "analysis_type": item.analysis_type,
                    "creation_date": item.creation_date.isoformat() if item.creation_date else None,
                    "submitted_by": item.submitted_by
                }
                for item in items
            ],
            "count": len(items)
        }
    except Exception as e:
        logger.error(f"Error getting new criticism queue items: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/queue")
async def create_criticism_queue_item(
    item: CriticismQueueCreate,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """Create a new criticism analysis request in the queue"""
    try:
        # Ensure user can only create criticism items for their tenant
        if item.tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: cannot create criticism items for other tenants"
            )

        new_item = await ProposalCriticismQueueService.create_item(
            db=db,
            opportunity_id=item.opportunity_id,
            tenant_id=item.tenant_id,
            client_short_name=item.client_short_name,
            priority=item.priority,
            analysis_type=item.analysis_type,
            submitted_by=item.submitted_by or current_user.email
        )

        if not new_item:
            raise HTTPException(status_code=500, detail="Failed to create criticism queue item")

        logger.info(f"Criticism analysis queued by user: {current_user.email}")
        return {
            "message": "Criticism analysis queued successfully",
            "id": new_item.id,
            "opportunity_id": new_item.opportunity_id
        }
        
    except Exception as e:
        logger.error(f"Error creating criticism queue item: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/queue/{item_id}/status")
async def update_criticism_queue_status(
    item_id: int,
    update: CriticismQueueStatusUpdate,
    db: AsyncSession = Depends(get_customer_db)
):
    """Update the status of a criticism queue item"""
    try:
        success = await ProposalCriticismQueueService.update_status(
            db=db,
            item_id=item_id,
            status=update.status,
            analysis_results=update.analysis_results,
            error_message=update.error_message
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Criticism queue item not found")
        
        return {"message": "Criticism queue item status updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating criticism queue status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/queue/{item_id}")
async def get_criticism_queue_item(
    item_id: int,
    db: AsyncSession = Depends(get_customer_db)
):
    """Get a specific criticism queue item by ID"""
    try:
        item = await ProposalCriticismQueueService.get_by_id(db, item_id)
        
        if not item:
            raise HTTPException(status_code=404, detail="Criticism queue item not found")
        
        return {
            "id": item.id,
            "opportunity_id": item.opportunity_id,
            "tenant_id": item.tenant_id,
            "client_short_name": item.client_short_name,
            "status": item.status,
            "priority": item.priority,
            "analysis_type": item.analysis_type,
            "creation_date": item.creation_date.isoformat() if item.creation_date else None,
            "completion_date": item.completion_date.isoformat() if item.completion_date else None,
            "submitted_by": item.submitted_by,
            "analysis_results": item.analysis_results,
            "error_message": item.error_message
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting criticism queue item {item_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/results/{opportunity_id}")
async def get_criticism_results(
    opportunity_id: str,
    tenant_id: str,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """Get criticism analysis results for a specific proposal"""
    try:
        # Ensure user can only get criticism results for their tenant
        if tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: cannot get criticism results for other tenants"
            )

        criticism_result = await ProposalCriticismController.get_criticism_by_opportunity(
            db, opportunity_id, tenant_id
        )

        if not criticism_result:
            raise HTTPException(status_code=404, detail="No criticism results found for this proposal")
        
        return {
            "opportunity_id": criticism_result.opportunity_id,
            "tenant_id": criticism_result.tenant_id,
            "analysis_timestamp": criticism_result.analysis_timestamp.isoformat(),
            "generation_method": criticism_result.generation_method,
            "scores": {
                "overall": criticism_result.overall_score,
                "structure": criticism_result.structure_score,
                "content": criticism_result.content_score,
                "formatting": criticism_result.formatting_score,
                "technical": criticism_result.technical_score
            },
            "summary": {
                "total_issues": criticism_result.total_issues,
                "total_strengths": criticism_result.total_strengths,
                "improvement_suggestions": criticism_result.improvement_suggestions,
                "analyzed_sections_count": criticism_result.analyzed_sections_count
            },
            "processing_time": criticism_result.processing_time_seconds,
            "detailed_results": {
                "structure_criticism": criticism_result.structure_criticism,
                "content_criticism": criticism_result.content_criticism,
                "formatting_criticism": criticism_result.formatting_criticism,
                "technical_criticism": criticism_result.technical_criticism
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting criticism results for {opportunity_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/{tenant_id}", response_model=CriticismResultsResponse)
async def get_criticism_statistics(
    tenant_id: str,
    days: int = 30,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """Get criticism statistics and recent results for a tenant"""
    try:
        # Ensure user can only get statistics for their tenant
        if tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: cannot get statistics for other tenants"
            )

        statistics = await ProposalCriticismController.get_criticism_statistics(
            db, tenant_id, days
        )

        recent_criticisms = await ProposalCriticismController.get_recent_criticisms(
            db, tenant_id, hours=24, limit=10
        )
        
        results = [
            {
                "opportunity_id": criticism.opportunity_id,
                "analysis_timestamp": criticism.analysis_timestamp.isoformat(),
                "overall_score": criticism.overall_score,
                "generation_method": criticism.generation_method,
                "total_issues": criticism.total_issues,
                "total_strengths": criticism.total_strengths
            }
            for criticism in recent_criticisms
        ]
        
        return CriticismResultsResponse(
            success=True,
            results=results,
            statistics=statistics
        )
        
    except Exception as e:
        logger.error(f"Error getting criticism statistics for {tenant_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get criticism statistics: {str(e)}")


@router.get("/queue/status/summary")
async def get_criticism_queue_summary(
    db: AsyncSession = Depends(get_customer_db)
):
    """Get summary of criticism queue status"""
    try:
        summary = await ProposalCriticismQueueService.get_queue_summary(db)
        return summary
        
    except Exception as e:
        logger.error(f"Error getting criticism queue summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/queue/{item_id}")
async def delete_criticism_queue_item(
    item_id: int,
    db: AsyncSession = Depends(get_customer_db)
):
    """Delete a criticism queue item"""
    try:
        success = await ProposalCriticismQueueService.delete_item(db, item_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Criticism queue item not found")
        
        return {"message": "Criticism queue item deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting criticism queue item {item_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
